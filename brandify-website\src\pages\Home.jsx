import { useEffect } from 'react';
import Hero from '../components/sections/Hero';
import ServicesOverview from '../components/sections/ServicesOverview';
import AboutPreview from '../components/sections/AboutPreview';
import Testimonials from '../components/sections/Testimonials';
import ClientLogos from '../components/sections/ClientLogos';
import CTA from '../components/sections/CTA';

const Home = () => {
  useEffect(() => {
    document.title = 'Brandify - Empowering Brands Digitally | Digital Marketing Agency';
    document.querySelector('meta[name="description"]')?.setAttribute('content', 
      'Brandify is a leading digital marketing agency helping businesses grow online through SEO, social media marketing, PPC advertising, and web development services.'
    );
  }, []);

  return (
    <div>
      <Hero />
      <ServicesOverview />
      <AboutPreview />
      <Testimonials />
      <ClientLogos />
      <CTA />
    </div>
  );
};

export default Home;
