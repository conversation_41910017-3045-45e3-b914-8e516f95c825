# Brandify Frontend

A modern, responsive, frontend-only website for Brandify Digital Marketing Agency, built with React and Tailwind CSS.

## 🚀 Features
- Fully responsive, professional design
- React Router navigation
- Tailwind CSS styling
- Poppins Google Font
- Animated scroll effects (AOS)
- Reusable components for cards, sections, and UI
- SEO-ready with per-page meta tags
- Floating WhatsApp button
- No backend required (static hosting ready)

## 📦 Project Structure
```
/src
  /components
    Navbar.jsx
    Footer.jsx
    Hero.jsx
    ServiceCard.jsx
    Testimonial.jsx
    CaseStudyCard.jsx
    BlogCard.jsx
    ClientLogoGrid.jsx
    ContactForm.jsx
    CTASection.jsx
    TeamMemberCard.jsx
    WhatsAppButton.jsx
  /pages
    Home.jsx
    About.jsx
    Services.jsx
    CaseStudies.jsx
    Blog.jsx
    Contact.jsx
  App.jsx
  index.css
```

## 🛠️ Getting Started

### 1. Install dependencies
```bash
npm install
```

### 2. Start the development server
```bash
npm start
```

### 3. Build for production
```bash
npm run build
```

## 🌐 Deployment
- Deploy the `build` folder to any static hosting (Hostinger, Netlify, Vercel, etc.)

## ✨ Customization
- Update brand colors, logo, and content in the respective components.
- Add or edit testimonials, case studies, and blog posts in their components/pages.

## 📄 License
MIT